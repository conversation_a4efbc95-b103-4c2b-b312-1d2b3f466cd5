{% extends "base.html" %}

{% block title %}性能评估 - 实体匹配与去重系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    性能评估与分析
                </h4>
            </div>
            <div class="card-body">
                {% if not completed %}
                <!-- 评估启动 -->
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-calculator me-2"></i>
                            计算评估指标
                        </h5>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>评估内容：</strong>
                            <ul class="mb-0 mt-2">
                                <li>计算Precision（精确率）、Recall（召回率）、F1-Score等核心指标</li>
                                <li>分析各个sheet的匹配性能表现</li>
                                <li>生成详细的性能报告和改进建议</li>
                                <li>特别评估Sheet8的负样本检测能力</li>
                            </ul>
                        </div>
                        
                        <form method="POST" id="evaluationForm">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="evaluationButton">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span class="button-text">
                                        <i class="fas fa-play me-2"></i>
                                        开始性能评估
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-bar text-info me-2"></i>
                                    评估指标
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Precision：</strong>预测正确的比例</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Recall：</strong>实际正例的覆盖率</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>F1-Score：</strong>综合性能指标</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Accuracy：</strong>整体准确率</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card bg-light mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    特殊说明
                                </h6>
                                <p class="small mb-0">
                                    Sheet8是负样本测试，匹配率越低表示算法性能越好。
                                    系统会提供专门的负样本评估指标。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <!-- 评估结果展示 -->
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>性能评估完成！</strong>已完成所有匹配结果的性能评估分析。
                </div>
                
                <!-- Test_01评估结果 -->
                {% if results.test_01 %}
                <div class="card mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-csv me-2"></i>
                            Test_01 性能评估
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-primary">{{ "%.3f"|format(results.test_01.precision) }}</div>
                                    <div class="metric-label">Precision</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-success">{{ "%.3f"|format(results.test_01.recall) }}</div>
                                    <div class="metric-label">Recall</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-info">{{ "%.3f"|format(results.test_01.f1_score) }}</div>
                                    <div class="metric-label">F1-Score</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-warning">{{ "%.3f"|format(results.test_01.accuracy) }}</div>
                                    <div class="metric-label">Accuracy</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6>详细统计：</h6>
                                <ul class="list-unstyled">
                                    <li><strong>总查询数：</strong> {{ "{:,}".format(results.test_01.total_queries) }}</li>
                                    <li><strong>成功匹配：</strong> {{ "{:,}".format(results.test_01.total_matches) }}</li>
                                    <li><strong>真正例：</strong> {{ "{:,}".format(results.test_01.true_positives) }}</li>
                                    <li><strong>假正例：</strong> {{ "{:,}".format(results.test_01.false_positives) }}</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>相似度分布：</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small>0.9+: {{ results.test_01.similarity_distribution['0.9+'] }}</small><br>
                                        <small>0.8-0.9: {{ results.test_01.similarity_distribution['0.8-0.9'] }}</small>
                                    </div>
                                    <div class="col-6">
                                        <small>0.7-0.8: {{ results.test_01.similarity_distribution['0.7-0.8'] }}</small><br>
                                        <small>&lt;0.7: {{ results.test_01.similarity_distribution['<0.6'] }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Test_02评估结果 -->
                {% if results.test_02 %}
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-excel me-2"></i>
                            Test_02 各Sheet性能评估
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sheet</th>
                                        <th>数据变换</th>
                                        <th>Precision</th>
                                        <th>Recall</th>
                                        <th>F1-Score</th>
                                        <th>Accuracy</th>
                                        <th>性能评级</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sheet_name, sheet_eval in results.test_02.items() %}
                                    <tr>
                                        <td><strong>{{ sheet_name }}</strong></td>
                                        <td>
                                            <span class="small">{{ sheet_eval.data_transformation }}</span>
                                        </td>
                                        <td>
                                            {% if sheet_name == 'Sheet8' %}
                                                <span class="text-muted">N/A</span>
                                            {% else %}
                                                {{ "%.3f"|format(sheet_eval.precision) }}
                                            {% endif %}
                                        </td>
                                        <td>{{ "%.3f"|format(sheet_eval.recall) }}</td>
                                        <td>
                                            {% if sheet_name == 'Sheet8' %}
                                                <span class="text-muted">N/A</span>
                                            {% else %}
                                                {{ "%.3f"|format(sheet_eval.f1_score) }}
                                            {% endif %}
                                        </td>
                                        <td>{{ "%.3f"|format(sheet_eval.accuracy) }}</td>
                                        <td>
                                            {% if sheet_name == 'Sheet8' %}
                                                {% if sheet_eval.false_positive_rate < 0.05 %}
                                                    <span class="badge bg-success">优秀</span>
                                                {% elif sheet_eval.false_positive_rate < 0.1 %}
                                                    <span class="badge bg-warning">良好</span>
                                                {% else %}
                                                    <span class="badge bg-danger">需改进</span>
                                                {% endif %}
                                            {% else %}
                                                {% if sheet_eval.f1_score > 0.8 %}
                                                    <span class="badge bg-success">优秀</span>
                                                {% elif sheet_eval.f1_score > 0.6 %}
                                                    <span class="badge bg-warning">良好</span>
                                                {% else %}
                                                    <span class="badge bg-danger">需改进</span>
                                                {% endif %}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Sheet8特殊评估 -->
                {% if results.test_02 and 'Sheet8' in results.test_02 %}
                <div class="card mt-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Sheet8 负样本检测评估
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-danger">{{ "%.1f"|format(results.test_02.Sheet8.false_positive_rate * 100) }}%</div>
                                    <div class="metric-label">假正例率</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-success">{{ "%.1f"|format(results.test_02.Sheet8.specificity * 100) }}%</div>
                                    <div class="metric-label">特异性</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-info">{{ "{:,}".format(results.test_02.Sheet8.true_negatives) }}</div>
                                    <div class="metric-label">正确拒绝数</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-warning">{{ "{:,}".format(results.test_02.Sheet8.false_positives) }}</div>
                                    <div class="metric-label">错误匹配数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>负样本评估说明：</strong>
                            {{ results.test_02.Sheet8.performance_note }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- 总体性能总结 -->
                {% if results.summary %}
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            总体性能总结
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if results.summary.overall_performance %}
                        <div class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-primary">{{ "%.3f"|format(results.summary.overall_performance.avg_f1_score) }}</div>
                                    <div class="metric-label">平均F1-Score</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-success">{{ "%.3f"|format(results.summary.overall_performance.max_f1_score) }}</div>
                                    <div class="metric-label">最高F1-Score</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-warning">{{ "%.3f"|format(results.summary.overall_performance.min_f1_score) }}</div>
                                    <div class="metric-label">最低F1-Score</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value text-info">{{ "%.3f"|format(results.summary.overall_performance.std_f1_score) }}</div>
                                    <div class="metric-label">F1标准差</div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6>表现最佳的Sheet：</h6>
                                <ul class="list-unstyled">
                                    {% for sheet_name, f1_score in results.summary.best_performing_sheets %}
                                    <li><i class="fas fa-trophy text-warning me-2"></i><strong>{{ sheet_name }}:</strong> F1 = {{ "%.3f"|format(f1_score) }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>需要改进的Sheet：</h6>
                                <ul class="list-unstyled">
                                    {% for sheet_name, f1_score in results.summary.worst_performing_sheets %}
                                    <li><i class="fas fa-exclamation-triangle text-danger me-2"></i><strong>{{ sheet_name }}:</strong> F1 = {{ "%.3f"|format(f1_score) }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6>改进建议：</h6>
                            <ul class="list-unstyled">
                                {% for recommendation in results.summary.recommendations %}
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>{{ recommendation }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- 操作按钮 -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-download text-success me-2"></i>
                                    导出完整结果
                                </h5>
                                <p class="card-text">
                                    下载所有匹配结果和评估报告
                                </p>
                                <a href="{{ url_for('export_results') }}" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    导出所有文件
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-home text-primary me-2"></i>
                                    返回首页
                                </h5>
                                <p class="card-text">
                                    回到系统首页查看整体流程
                                </p>
                                <a href="{{ url_for('index') }}" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>
                                    返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单提交处理
        const form = document.getElementById('evaluationForm');
        const button = document.getElementById('evaluationButton');
        
        if (form && button) {
            form.addEventListener('submit', function(e) {
                showLoading('evaluationButton');
            });
        }
        
        {% if completed %}
        // 结果展示动画
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
        
        // 创建性能图表
        createPerformanceChart();
        {% endif %}
    });
    
    function createPerformanceChart() {
        // 这里可以添加Chart.js图表
        // 例如F1-Score对比图、相似度分布图等
    }
</script>
{% endblock %}
