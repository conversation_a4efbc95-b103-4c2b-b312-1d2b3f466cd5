#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去重模块
Deduplication Module
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
import logging
from .preprocessor import TextPreprocessor
from .similarity_calculator import SimilarityCalculator

logger = logging.getLogger(__name__)

class EntityDeduplicator:
    """实体去重器类"""
    
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        self.similarity_calc = SimilarityCalculator()
    
    def deduplicate_primary(self, 
                          primary_df: pd.DataFrame,
                          threshold: float = 0.8,
                          method: str = 'combined') -> pd.DataFrame:
        """
        对primary数据进行去重
        
        Args:
            primary_df: primary数据集
            threshold: 相似度阈值
            method: 相似度计算方法
            
        Returns:
            pd.DataFrame: 去重后的数据
        """
        logger.info(f"开始primary数据去重，原始数据: {len(primary_df)} 条")
        
        # 复制数据
        df = primary_df.copy()
        
        # 添加处理标记
        df['processed'] = False
        df['duplicate_group'] = -1
        
        duplicates_to_remove = set()
        group_id = 0
        
        for idx, row in df.iterrows():
            if df.loc[idx, 'processed']:
                continue
            
            current_name = str(row['NAME'])
            current_group = [idx]
            
            # 查找相似的实体
            for other_idx, other_row in df.iterrows():
                if other_idx <= idx or df.loc[other_idx, 'processed']:
                    continue
                
                other_name = str(other_row['NAME'])
                
                # 计算相似度
                similarity = self._calculate_similarity(current_name, other_name, method)
                
                if similarity >= threshold:
                    current_group.append(other_idx)
                    df.loc[other_idx, 'processed'] = True
                    df.loc[other_idx, 'duplicate_group'] = group_id
            
            # 标记当前实体
            df.loc[idx, 'processed'] = True
            df.loc[idx, 'duplicate_group'] = group_id
            
            # 如果找到重复项，保留最完整的记录
            if len(current_group) > 1:
                best_idx = self._select_best_record(df.loc[current_group])
                for dup_idx in current_group:
                    if dup_idx != best_idx:
                        duplicates_to_remove.add(dup_idx)
            
            group_id += 1
        
        # 移除重复项
        deduped_df = df.drop(duplicates_to_remove).copy()
        deduped_df = deduped_df.drop(['processed', 'duplicate_group'], axis=1)
        
        logger.info(f"Primary去重完成，去重后数据: {len(deduped_df)} 条，移除: {len(duplicates_to_remove)} 条")
        
        return deduped_df
    
    def deduplicate_alternate(self, 
                            alternate_df: pd.DataFrame,
                            primary_df: pd.DataFrame,
                            threshold: float = 0.8,
                            method: str = 'combined') -> pd.DataFrame:
        """
        对alternate数据进行去重（与primary数据比较）
        
        Args:
            alternate_df: alternate数据集
            primary_df: primary数据集
            threshold: 相似度阈值
            method: 相似度计算方法
            
        Returns:
            pd.DataFrame: 去重后的数据
        """
        logger.info(f"开始alternate数据去重，原始数据: {len(alternate_df)} 条")
        
        # 复制数据
        df = alternate_df.copy()
        df['is_duplicate'] = False
        df['matched_primary_id'] = None
        df['similarity_score'] = 0.0
        
        # 与primary数据比较
        for idx, alt_row in df.iterrows():
            alt_name = str(alt_row['NAME'])
            alt_id = alt_row['ID']
            
            best_match = self._find_primary_match(alt_name, alt_id, primary_df, threshold, method)
            
            if best_match is not None:
                df.loc[idx, 'is_duplicate'] = True
                df.loc[idx, 'matched_primary_id'] = best_match['id']
                df.loc[idx, 'similarity_score'] = best_match['score']
        
        # 内部去重（alternate数据内部的重复）
        df = self._deduplicate_within_alternate(df, threshold, method)
        
        # 移除重复项
        deduped_df = df[~df['is_duplicate']].copy()
        deduped_df = deduped_df.drop(['is_duplicate', 'matched_primary_id', 'similarity_score'], axis=1)
        
        logger.info(f"Alternate去重完成，去重后数据: {len(deduped_df)} 条，移除: {len(df) - len(deduped_df)} 条")
        
        return deduped_df
    
    def _calculate_similarity(self, text1: str, text2: str, method: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1, text2: 两个文本
            method: 相似度计算方法
            
        Returns:
            float: 相似度分数
        """
        # 预处理文本
        processed_text1 = self.preprocessor.normalize_text(text1)
        processed_text2 = self.preprocessor.normalize_text(text2)
        
        if method == 'levenshtein':
            return self.similarity_calc.levenshtein_similarity(processed_text1, processed_text2)
        elif method == 'jaro_winkler':
            return self.similarity_calc.jaro_winkler_similarity(processed_text1, processed_text2)
        elif method == 'jaccard':
            return self.similarity_calc.jaccard_token_similarity(processed_text1, processed_text2)
        elif method == 'combined':
            return self.similarity_calc.combined_similarity(processed_text1, processed_text2)
        else:
            return self.similarity_calc.combined_similarity(processed_text1, processed_text2)
    
    def _select_best_record(self, group_df: pd.DataFrame) -> int:
        """
        从重复组中选择最佳记录
        
        Args:
            group_df: 重复组数据
            
        Returns:
            int: 最佳记录的索引
        """
        # 选择策略：
        # 1. 名称最长的记录
        # 2. 如果长度相同，选择ID最小的
        
        best_idx = None
        max_length = 0
        min_id = float('inf')
        
        for idx, row in group_df.iterrows():
            name_length = len(str(row['NAME']))
            current_id = row['ID']
            
            if (name_length > max_length or 
                (name_length == max_length and current_id < min_id)):
                max_length = name_length
                min_id = current_id
                best_idx = idx
        
        return best_idx
    
    def _find_primary_match(self, 
                          alt_name: str, 
                          alt_id: int,
                          primary_df: pd.DataFrame,
                          threshold: float,
                          method: str) -> Dict:
        """
        在primary数据中查找匹配
        
        Args:
            alt_name: alternate实体名称
            alt_id: alternate实体ID
            primary_df: primary数据集
            threshold: 相似度阈值
            method: 相似度计算方法
            
        Returns:
            Dict: 匹配信息，如果没有找到则返回None
        """
        best_score = 0.0
        best_match = None
        
        # 首先检查ID是否直接匹配
        id_matches = primary_df[primary_df['ID'] == alt_id]
        if len(id_matches) > 0:
            # ID匹配，检查名称相似度
            for idx, primary_row in id_matches.iterrows():
                primary_name = str(primary_row['NAME'])
                similarity = self._calculate_similarity(alt_name, primary_name, method)
                
                if similarity >= threshold * 0.7:  # 对于ID匹配的情况，降低阈值
                    return {
                        'id': primary_row['ID'],
                        'name': primary_name,
                        'score': similarity,
                        'match_type': 'id_and_name'
                    }
        
        # 如果ID不匹配，进行名称匹配
        for idx, primary_row in primary_df.iterrows():
            primary_name = str(primary_row['NAME'])
            similarity = self._calculate_similarity(alt_name, primary_name, method)
            
            if similarity > best_score and similarity >= threshold:
                best_score = similarity
                best_match = {
                    'id': primary_row['ID'],
                    'name': primary_name,
                    'score': similarity,
                    'match_type': 'name_only'
                }
        
        return best_match
    
    def _deduplicate_within_alternate(self, 
                                    df: pd.DataFrame,
                                    threshold: float,
                                    method: str) -> pd.DataFrame:
        """
        在alternate数据内部进行去重
        
        Args:
            df: alternate数据集
            threshold: 相似度阈值
            method: 相似度计算方法
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        # 只处理未标记为重复的记录
        non_duplicates = df[~df['is_duplicate']].copy()
        
        if len(non_duplicates) <= 1:
            return df
        
        processed = set()
        
        for idx, row in non_duplicates.iterrows():
            if idx in processed:
                continue
            
            current_name = str(row['NAME'])
            duplicates_found = []
            
            for other_idx, other_row in non_duplicates.iterrows():
                if other_idx <= idx or other_idx in processed:
                    continue
                
                other_name = str(other_row['NAME'])
                similarity = self._calculate_similarity(current_name, other_name, method)
                
                if similarity >= threshold:
                    duplicates_found.append(other_idx)
            
            # 如果找到重复项，保留最佳记录
            if duplicates_found:
                all_duplicates = [idx] + duplicates_found
                group_data = non_duplicates.loc[all_duplicates]
                best_idx = self._select_best_record(group_data)
                
                # 标记其他记录为重复
                for dup_idx in all_duplicates:
                    if dup_idx != best_idx:
                        df.loc[dup_idx, 'is_duplicate'] = True
                        processed.add(dup_idx)
                
                processed.add(best_idx)
        
        return df
    
    def get_deduplication_report(self, 
                               original_df: pd.DataFrame,
                               deduped_df: pd.DataFrame,
                               data_type: str) -> Dict:
        """
        生成去重报告
        
        Args:
            original_df: 原始数据
            deduped_df: 去重后数据
            data_type: 数据类型
            
        Returns:
            Dict: 去重报告
        """
        report = {
            'data_type': data_type,
            'original_count': len(original_df),
            'deduped_count': len(deduped_df),
            'removed_count': len(original_df) - len(deduped_df),
            'deduplication_rate': (len(original_df) - len(deduped_df)) / len(original_df) * 100,
            'unique_ids': {
                'original': original_df['ID'].nunique(),
                'deduped': deduped_df['ID'].nunique()
            }
        }
        
        return report
