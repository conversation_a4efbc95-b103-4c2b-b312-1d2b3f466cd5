#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实体匹配与去重系统 - Flask Web应用
Entity Matching and Deduplication System - Flask Web Application
"""

from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
import logging
from werkzeug.utils import secure_filename
import zipfile
import io

# 导入自定义模块
from modules.data_loader import DataLoader
from modules.preprocessor import TextPreprocessor
from modules.similarity_calculator import SimilarityCalculator
from modules.matcher import EntityMatcher
from modules.deduplicator import EntityDeduplicator
from modules.evaluator import PerformanceEvaluator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'entity_matching_secret_key_2024'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# 全局变量存储数据和结果
data_store = {
    'primary_df': None,
    'alternate_df': None,
    'test_01_df': None,
    'test_02_sheets': {},
    'dedup_results': {},
    'match_results': {},
    'evaluation_results': {}
}

# 初始化模块
data_loader = DataLoader()
preprocessor = TextPreprocessor()
similarity_calc = SimilarityCalculator()
matcher = EntityMatcher()
deduplicator = EntityDeduplicator()
evaluator = PerformanceEvaluator()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/load_data', methods=['GET', 'POST'])
def load_data():
    """数据加载页面"""
    if request.method == 'POST':
        try:
            # 加载数据文件
            data_store['primary_df'] = data_loader.load_primary_data('DATA/primary.csv')
            data_store['alternate_df'] = data_loader.load_alternate_data('DATA/alternate.csv')
            data_store['test_01_df'] = data_loader.load_test_01_data('DATA/test_01.csv')
            data_store['test_02_sheets'] = data_loader.load_test_02_data('DATA/test_02.xlsx')
            
            # 统计信息
            stats = {
                'primary_count': len(data_store['primary_df']),
                'alternate_count': len(data_store['alternate_df']),
                'test_01_count': len(data_store['test_01_df']),
                'test_02_sheets': {k: len(v) for k, v in data_store['test_02_sheets'].items() if k != 'Desc'}
            }
            
            flash('数据加载成功！', 'success')
            return render_template('load_data.html', stats=stats, loaded=True)
            
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            flash(f'数据加载失败: {str(e)}', 'error')
            return render_template('load_data.html', loaded=False)
    
    return render_template('load_data.html', loaded=False)

@app.route('/deduplication', methods=['GET', 'POST'])
def deduplication():
    """去重处理页面"""
    if request.method == 'POST':
        try:
            # 检查数据是否已加载
            if data_store['primary_df'] is None or data_store['alternate_df'] is None:
                flash('请先加载数据！', 'error')
                return redirect(url_for('load_data'))
            
            # 获取参数
            similarity_threshold = float(request.form.get('similarity_threshold', 0.8))
            method = request.form.get('method', 'combined')
            
            # 执行去重
            logger.info("开始执行去重处理...")
            
            # Primary数据去重
            primary_dedup = deduplicator.deduplicate_primary(
                data_store['primary_df'], 
                threshold=similarity_threshold,
                method=method
            )
            
            # Alternate数据去重
            alternate_dedup = deduplicator.deduplicate_alternate(
                data_store['alternate_df'],
                data_store['primary_df'],
                threshold=similarity_threshold,
                method=method
            )
            
            # 存储结果
            data_store['dedup_results'] = {
                'primary_dedup': primary_dedup,
                'alternate_dedup': alternate_dedup,
                'primary_original_count': len(data_store['primary_df']),
                'primary_dedup_count': len(primary_dedup),
                'alternate_original_count': len(data_store['alternate_df']),
                'alternate_dedup_count': len(alternate_dedup),
                'parameters': {
                    'similarity_threshold': similarity_threshold,
                    'method': method
                }
            }
            
            flash('去重处理完成！', 'success')
            return render_template('deduplication.html', 
                                 results=data_store['dedup_results'], 
                                 completed=True)
            
        except Exception as e:
            logger.error(f"去重处理失败: {str(e)}")
            flash(f'去重处理失败: {str(e)}', 'error')
            return render_template('deduplication.html', completed=False)
    
    return render_template('deduplication.html', completed=False)

@app.route('/matching', methods=['GET', 'POST'])
def matching():
    """实体匹配页面"""
    if request.method == 'POST':
        try:
            # 检查数据是否已加载
            if data_store['primary_df'] is None:
                flash('请先加载数据！', 'error')
                return redirect(url_for('load_data'))
            
            # 获取参数
            similarity_threshold = float(request.form.get('similarity_threshold', 0.7))
            method = request.form.get('method', 'combined')
            
            logger.info("开始执行实体匹配...")
            
            # 执行test_01匹配
            test_01_matches = matcher.match_entities(
                data_store['test_01_df'],
                data_store['primary_df'],
                threshold=similarity_threshold,
                method=method,
                query_col='VARIANT',
                target_col='NAME'
            )
            
            # 执行test_02各sheet匹配
            test_02_matches = {}
            for sheet_name, sheet_df in data_store['test_02_sheets'].items():
                if sheet_name == 'Desc':
                    continue
                    
                logger.info(f"匹配 {sheet_name}...")
                matches = matcher.match_entities(
                    sheet_df,
                    data_store['primary_df'],
                    threshold=similarity_threshold,
                    method=method,
                    query_col='NAME',
                    target_col='NAME',
                    is_negative_sample=(sheet_name == 'Sheet8')
                )
                test_02_matches[sheet_name] = matches
            
            # 存储结果
            data_store['match_results'] = {
                'test_01_matches': test_01_matches,
                'test_02_matches': test_02_matches,
                'parameters': {
                    'similarity_threshold': similarity_threshold,
                    'method': method
                }
            }
            
            flash('实体匹配完成！', 'success')
            return render_template('matching.html', 
                                 results=data_store['match_results'], 
                                 completed=True)
            
        except Exception as e:
            logger.error(f"实体匹配失败: {str(e)}")
            flash(f'实体匹配失败: {str(e)}', 'error')
            return render_template('matching.html', completed=False)
    
    return render_template('matching.html', completed=False)

@app.route('/evaluation', methods=['GET', 'POST'])
def evaluation():
    """性能评估页面"""
    if request.method == 'POST':
        try:
            # 检查匹配结果是否存在
            if not data_store['match_results']:
                flash('请先执行实体匹配！', 'error')
                return redirect(url_for('matching'))
            
            logger.info("开始计算评估指标...")
            
            # 计算评估指标
            evaluation_results = evaluator.evaluate_all_results(
                data_store['match_results'],
                data_store['test_01_df'],
                data_store['test_02_sheets']
            )
            
            # 存储结果
            data_store['evaluation_results'] = evaluation_results
            
            flash('性能评估完成！', 'success')
            return render_template('evaluation.html', 
                                 results=evaluation_results, 
                                 completed=True)
            
        except Exception as e:
            logger.error(f"性能评估失败: {str(e)}")
            flash(f'性能评估失败: {str(e)}', 'error')
            return render_template('evaluation.html', completed=False)
    
    return render_template('evaluation.html', completed=False)

@app.route('/export_results')
def export_results():
    """导出结果文件"""
    try:
        # 创建内存中的ZIP文件
        memory_file = io.BytesIO()
        
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # 导出去重结果
            if data_store['dedup_results']:
                # Primary去重结果
                primary_dedup_csv = data_store['dedup_results']['primary_dedup'].to_csv(index=False)
                zf.writestr('primary_deduped.csv', primary_dedup_csv)
                
                # Alternate去重结果
                alternate_dedup_csv = data_store['dedup_results']['alternate_dedup'].to_csv(index=False)
                zf.writestr('alternate_deduped.csv', alternate_dedup_csv)
            
            # 导出匹配结果
            if data_store['match_results']:
                # Test_01匹配结果
                test_01_csv = data_store['match_results']['test_01_matches'].to_csv(index=False)
                zf.writestr('test_01_matches.csv', test_01_csv)
                
                # Test_02各sheet匹配结果
                for sheet_name, matches_df in data_store['match_results']['test_02_matches'].items():
                    csv_content = matches_df.to_csv(index=False)
                    zf.writestr(f'test_02_{sheet_name.lower()}_matches.csv', csv_content)
            
            # 导出评估结果
            if data_store['evaluation_results']:
                eval_json = json.dumps(data_store['evaluation_results'], indent=2, ensure_ascii=False)
                zf.writestr('evaluation_report.json', eval_json)
        
        memory_file.seek(0)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'entity_matching_results_{timestamp}.zip'
        
        return send_file(
            memory_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/zip'
        )
        
    except Exception as e:
        logger.error(f"导出结果失败: {str(e)}")
        flash(f'导出结果失败: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/api/progress')
def get_progress():
    """获取处理进度（用于AJAX）"""
    # 这里可以实现实时进度更新
    return jsonify({'status': 'running', 'progress': 50})

if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('modules', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
