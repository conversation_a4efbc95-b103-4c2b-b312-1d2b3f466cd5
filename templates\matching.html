{% extends "base.html" %}

{% block title %}实体匹配 - 实体匹配与去重系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    智能实体匹配
                </h4>
            </div>
            <div class="card-body">
                {% if not completed %}
                <!-- 匹配配置表单 -->
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-cogs me-2"></i>
                            匹配参数配置
                        </h5>
                        
                        <form method="POST" id="matchingForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="similarity_threshold" class="form-label">
                                            <i class="fas fa-sliders-h me-2"></i>
                                            相似度阈值
                                        </label>
                                        <input type="range" class="form-range" id="similarity_threshold" 
                                               name="similarity_threshold" min="0.5" max="0.95" step="0.05" value="0.7">
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">0.5 (宽松)</small>
                                            <small class="text-muted" id="thresholdValue">0.7</small>
                                            <small class="text-muted">0.95 (严格)</small>
                                        </div>
                                        <small class="form-text text-muted">
                                            匹配阈值，影响匹配的严格程度
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="method" class="form-label">
                                            <i class="fas fa-brain me-2"></i>
                                            匹配算法
                                        </label>
                                        <select class="form-select" id="method" name="method">
                                            <option value="combined" selected>自适应组合算法（推荐）</option>
                                            <option value="levenshtein">Levenshtein距离</option>
                                            <option value="jaro_winkler">Jaro-Winkler相似度</option>
                                            <option value="jaccard">Jaccard系数</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            自适应算法会根据不同sheet类型自动调整策略
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>匹配任务说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>Test_01匹配：</strong>将test_01.csv中的变体与primary.csv进行匹配</li>
                                    <li><strong>Test_02匹配：</strong>分别处理8个sheet，每个sheet有不同的数据变换</li>
                                    <li><strong>Sheet8特殊处理：</strong>负样本检测，匹配不到才是正常的</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>注意：</strong>匹配过程可能需要5-10分钟，请耐心等待。系统会自动处理所有数据集。
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="matchingButton">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span class="button-text">
                                        <i class="fas fa-play me-2"></i>
                                        开始实体匹配
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    匹配策略
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-2"></i><strong>多层次匹配：</strong>精确→模糊→语义</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>自适应阈值：</strong>根据数据类型调整</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>负样本检测：</strong>Sheet8特殊处理</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card bg-light mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-table text-info me-2"></i>
                                    处理数据集
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-file-csv text-primary me-2"></i>Test_01: 16,043条记录</li>
                                    <li><i class="fas fa-file-excel text-success me-2"></i>Test_02: 8个sheet × 1,000条</li>
                                    <li><i class="fas fa-database text-info me-2"></i>Primary: 16,043条标准数据</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <!-- 匹配结果展示 -->
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>实体匹配完成！</strong>已成功完成所有数据集的实体匹配处理。
                </div>
                
                <!-- Test_01匹配结果 -->
                <div class="card mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-csv me-2"></i>
                            Test_01 匹配结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <div class="metric-value text-primary">{{ "{:,}".format(results.test_01_matches|length) }}</div>
                                    <div class="metric-label">成功匹配数</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <div class="metric-value text-info">
                                        {% if results.test_01_matches|length > 0 %}
                                            {{ "%.3f"|format(results.test_01_matches['similarity_score'].mean()) }}
                                        {% else %}
                                            0.000
                                        {% endif %}
                                    </div>
                                    <div class="metric-label">平均相似度</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="metric-card">
                                    <div class="metric-value text-success">
                                        {{ "%.1f"|format((results.test_01_matches|length / 16043 * 100)) }}%
                                    </div>
                                    <div class="metric-label">匹配率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test_02匹配结果 -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-excel me-2"></i>
                            Test_02 各Sheet匹配结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sheet</th>
                                        <th>数据变换</th>
                                        <th>匹配数</th>
                                        <th>匹配率</th>
                                        <th>平均相似度</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sheet_name, matches_df in results.test_02_matches.items() %}
                                    <tr>
                                        <td><strong>{{ sheet_name }}</strong></td>
                                        <td>
                                            {% if sheet_name == 'Sheet1' %}
                                                <span class="badge bg-primary">移除特殊字符</span>
                                            {% elif sheet_name == 'Sheet2' %}
                                                <span class="badge bg-success">单词打乱</span>
                                            {% elif sheet_name == 'Sheet3' %}
                                                <span class="badge bg-info">字符分离</span>
                                            {% elif sheet_name == 'Sheet4' %}
                                                <span class="badge bg-warning">组合变换</span>
                                            {% elif sheet_name == 'Sheet5' %}
                                                <span class="badge bg-secondary">单词移除</span>
                                            {% elif sheet_name == 'Sheet6' %}
                                                <span class="badge bg-dark">单词截断</span>
                                            {% elif sheet_name == 'Sheet7' %}
                                                <span class="badge bg-light text-dark">待分析</span>
                                            {% elif sheet_name == 'Sheet8' %}
                                                <span class="badge bg-danger">负样本</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ "{:,}".format(matches_df|length) }}</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar 
                                                    {% if sheet_name == 'Sheet8' %}
                                                        {% if (matches_df|length / 1000 * 100) < 5 %}bg-success{% else %}bg-danger{% endif %}
                                                    {% else %}
                                                        {% if (matches_df|length / 1000 * 100) > 80 %}bg-success
                                                        {% elif (matches_df|length / 1000 * 100) > 60 %}bg-warning
                                                        {% else %}bg-danger{% endif %}
                                                    {% endif %}" 
                                                     role="progressbar" 
                                                     style="width: {{ (matches_df|length / 1000 * 100)|round(1) }}%">
                                                    {{ "%.1f"|format(matches_df|length / 1000 * 100) }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if matches_df|length > 0 %}
                                                {{ "%.3f"|format(matches_df['similarity_score'].mean()) }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if sheet_name == 'Sheet8' %}
                                                {% if (matches_df|length / 1000 * 100) < 5 %}
                                                    <span class="badge bg-success">优秀</span>
                                                {% else %}
                                                    <span class="badge bg-warning">需优化</span>
                                                {% endif %}
                                            {% else %}
                                                {% if (matches_df|length / 1000 * 100) > 80 %}
                                                    <span class="badge bg-success">优秀</span>
                                                {% elif (matches_df|length / 1000 * 100) > 60 %}
                                                    <span class="badge bg-warning">良好</span>
                                                {% else %}
                                                    <span class="badge bg-danger">需优化</span>
                                                {% endif %}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 匹配参数信息 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            匹配参数配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>相似度阈值：</strong> {{ results.parameters.similarity_threshold }}</p>
                                <p><strong>匹配算法：</strong> 
                                    {% if results.parameters.method == 'combined' %}
                                        自适应组合算法
                                    {% elif results.parameters.method == 'levenshtein' %}
                                        Levenshtein距离
                                    {% elif results.parameters.method == 'jaro_winkler' %}
                                        Jaro-Winkler相似度
                                    {% elif results.parameters.method == 'jaccard' %}
                                        Jaccard系数
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>总匹配数：</strong> 
                                    {% set total_matches = results.test_01_matches|length %}
                                    {% for sheet_name, matches_df in results.test_02_matches.items() %}
                                        {% set total_matches = total_matches + matches_df|length %}
                                    {% endfor %}
                                    {{ "{:,}".format(total_matches) }}
                                </p>
                                <p><strong>处理状态：</strong> <span class="badge bg-success">完成</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 下一步操作 -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    性能评估
                                </h5>
                                <p class="card-text">
                                    计算详细的评估指标和性能分析
                                </p>
                                <a href="{{ url_for('evaluation') }}" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    查看评估
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-download text-success me-2"></i>
                                    导出结果
                                </h5>
                                <p class="card-text">
                                    下载所有匹配结果文件
                                </p>
                                <a href="{{ url_for('export_results') }}" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    导出文件
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-redo text-warning me-2"></i>
                                    重新匹配
                                </h5>
                                <p class="card-text">
                                    使用不同参数重新执行匹配
                                </p>
                                <button class="btn btn-warning" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>
                                    重新配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 阈值滑块实时更新
        const thresholdSlider = document.getElementById('similarity_threshold');
        const thresholdValue = document.getElementById('thresholdValue');
        
        if (thresholdSlider && thresholdValue) {
            thresholdSlider.addEventListener('input', function() {
                thresholdValue.textContent = this.value;
            });
        }
        
        // 表单提交处理
        const form = document.getElementById('matchingForm');
        const button = document.getElementById('matchingButton');
        
        if (form && button) {
            form.addEventListener('submit', function(e) {
                showLoading('matchingButton');
            });
        }
        
        {% if completed %}
        // 结果展示动画
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            }, index * 100);
        });
        
        // 进度条动画
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach((bar, index) => {
            const width = bar.style.width;
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'width 1.5s ease';
                bar.style.width = width;
            }, 500 + index * 200);
        });
        {% endif %}
    });
</script>
{% endblock %}
