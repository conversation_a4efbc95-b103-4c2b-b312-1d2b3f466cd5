#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实体匹配模块
Entity Matching Module
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from .preprocessor import TextPreprocessor
from .similarity_calculator import SimilarityCalculator
import time
from collections import defaultdict
import re

logger = logging.getLogger(__name__)

class EntityMatcher:
    """实体匹配器类"""
    
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        self.similarity_calc = SimilarityCalculator()
        self._target_cache = {}  # 缓存预处理的目标数据
        self._similarity_cache = {}  # 缓存相似度计算结果
    
    def match_entities(self,
                      query_df: pd.DataFrame,
                      target_df: pd.DataFrame,
                      threshold: float = 0.7,
                      method: str = 'combined',
                      query_col: str = 'NAME',
                      target_col: str = 'NAME',
                      is_negative_sample: bool = False,
                      progress_callback=None) -> pd.DataFrame:
        """
        执行实体匹配
        
        Args:
            query_df: 查询数据集
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            query_col: 查询列名
            target_col: 目标列名
            is_negative_sample: 是否为负样本
            
        Returns:
            pd.DataFrame: 匹配结果
        """
        logger.info(f"开始实体匹配，查询数据: {len(query_df)} 条，目标数据: {len(target_df)} 条")
        
        matches = []
        total_queries = len(query_df)

        for i, (idx, query_row) in enumerate(query_df.iterrows()):
            # 更新进度
            if progress_callback and i % 100 == 0:
                progress = int((i / total_queries) * 100)
                progress_callback(progress)

            query_id = query_row['ID']
            query_text = str(query_row[query_col])

            # 对于负样本，使用更严格的阈值
            current_threshold = threshold * 1.2 if is_negative_sample else threshold

            best_match = self._find_best_match(
                query_text,
                target_df,
                target_col,
                current_threshold,
                method
            )

            if best_match is not None:
                matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method
                })

        # 最终进度更新
        if progress_callback:
            progress_callback(100)
        
        result_df = pd.DataFrame(matches)
        logger.info(f"匹配完成，找到 {len(result_df)} 个匹配")
        
        return result_df

    def fast_match_entities(self,
                           query_df: pd.DataFrame,
                           target_df: pd.DataFrame,
                           threshold: float = 0.7,
                           method: str = 'combined',
                           query_col: str = 'NAME',
                           target_col: str = 'NAME',
                           is_negative_sample: bool = False,
                           progress_callback=None) -> pd.DataFrame:
        """
        高性能实体匹配（优化版本）

        使用以下优化策略：
        1. 预处理缓存
        2. 早期终止
        3. 批量处理
        4. 智能阈值调整
        """
        logger.info(f"开始高性能实体匹配，查询数据: {len(query_df)} 条，目标数据: {len(target_df)} 条")

        # 预处理目标数据（一次性处理，缓存结果）
        target_cache_key = f"{id(target_df)}_{target_col}_{method}"
        if target_cache_key not in self._target_cache:
            logger.info("预处理目标数据...")
            self._preprocess_target_data(target_df, target_col, target_cache_key)

        processed_targets = self._target_cache[target_cache_key]

        matches = []
        total_queries = len(query_df)
        batch_size = 100  # 批量处理大小

        # 对于负样本，使用更严格的阈值
        current_threshold = threshold * 1.2 if is_negative_sample else threshold

        for i in range(0, total_queries, batch_size):
            batch_end = min(i + batch_size, total_queries)
            batch_df = query_df.iloc[i:batch_end]

            # 更新进度
            if progress_callback:
                progress = int((i / total_queries) * 100)
                progress_callback(progress)

            # 批量处理
            batch_matches = self._process_batch(
                batch_df, processed_targets, current_threshold, method, query_col
            )
            matches.extend(batch_matches)

            # 每处理1000条记录输出一次日志
            if (i + batch_size) % 1000 == 0:
                logger.info(f"已处理 {min(i + batch_size, total_queries)}/{total_queries} 条记录")

        # 最终进度更新
        if progress_callback:
            progress_callback(100)

        result_df = pd.DataFrame(matches)
        logger.info(f"高性能匹配完成，找到 {len(result_df)} 个匹配")

        return result_df

    def _preprocess_target_data(self, target_df: pd.DataFrame, target_col: str, cache_key: str):
        """预处理目标数据并缓存"""
        processed_targets = []

        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row[target_col])

            # 预处理文本
            processed_text = self.preprocessor.normalize_text(target_text)

            # 创建搜索变体（限制数量以提高性能）
            variants = self.preprocessor.create_search_variants(target_text)[:3]  # 只取前3个变体

            processed_targets.append({
                'id': target_id,
                'original': target_text,
                'processed': processed_text,
                'variants': variants
            })

        self._target_cache[cache_key] = processed_targets
        logger.info(f"目标数据预处理完成，缓存 {len(processed_targets)} 条记录")

    def _process_batch(self, batch_df: pd.DataFrame, processed_targets: List[Dict],
                      threshold: float, method: str, query_col: str) -> List[Dict]:
        """批量处理查询数据"""
        batch_matches = []

        for idx, query_row in batch_df.iterrows():
            query_id = query_row['ID']
            query_text = str(query_row[query_col])

            # 快速匹配
            best_match = self._fast_find_best_match(
                query_text, processed_targets, threshold, method
            )

            if best_match is not None:
                batch_matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method
                })

        return batch_matches

    def _fast_find_best_match(self, query_text: str, processed_targets: List[Dict],
                             threshold: float, method: str) -> Optional[Dict]:
        """快速查找最佳匹配"""
        best_score = 0.0
        best_match = None

        # 预处理查询文本
        processed_query = self.preprocessor.normalize_text(query_text)

        # 创建查询变体（限制数量）
        query_variants = [processed_query]
        additional_variants = self.preprocessor.create_search_variants(query_text)[:2]
        query_variants.extend(additional_variants)

        for target_data in processed_targets:
            # 快速预筛选：先用主要形式比较
            quick_score = self._calculate_similarity(processed_query, target_data['processed'], method)

            # 如果快速得分太低，跳过详细比较
            if quick_score < threshold * 0.8:
                continue

            # 详细比较所有变体
            max_score = quick_score
            for query_var in query_variants:
                for target_var in [target_data['processed']] + target_data['variants'][:2]:
                    score = self._calculate_similarity(query_var, target_var, method)
                    max_score = max(max_score, score)

                    # 早期终止：如果找到很高的分数，直接返回
                    if score > 0.95:
                        return {
                            'id': target_data['id'],
                            'name': target_data['original'],
                            'score': score
                        }

            # 更新最佳匹配
            if max_score > best_score and max_score >= threshold:
                best_score = max_score
                best_match = {
                    'id': target_data['id'],
                    'name': target_data['original'],
                    'score': max_score
                }

        return best_match

    def _calculate_similarity(self, text1: str, text2: str, method: str) -> float:
        """计算相似度（带缓存）"""
        # 创建缓存键
        cache_key = f"{text1}|{text2}|{method}"
        if cache_key in self._similarity_cache:
            return self._similarity_cache[cache_key]

        # 计算相似度
        if method == 'levenshtein':
            score = self.similarity_calc.levenshtein_similarity(text1, text2)
        elif method == 'jaro_winkler':
            score = self.similarity_calc.jaro_winkler_similarity(text1, text2)
        elif method == 'jaccard':
            score = self.similarity_calc.jaccard_token_similarity(text1, text2)
        elif method == 'combined':
            score = self.similarity_calc.combined_similarity(text1, text2)
        else:
            score = self.similarity_calc.combined_similarity(text1, text2)

        # 缓存结果（限制缓存大小）
        if len(self._similarity_cache) < 10000:
            self._similarity_cache[cache_key] = score

        return score

    def ultra_fast_match_entities(self,
                                 query_df: pd.DataFrame,
                                 target_df: pd.DataFrame,
                                 threshold: float = 0.7,
                                 method: str = 'combined',
                                 query_col: str = 'NAME',
                                 target_col: str = 'NAME',
                                 is_negative_sample: bool = False,
                                 progress_callback=None) -> pd.DataFrame:
        """
        超高速实体匹配 - 秒级完成

        使用简化策略：
        1. 完全相同匹配
        2. 简单字符串包含
        3. 长度相似性
        """
        logger.info(f"开始超高速实体匹配，查询数据: {len(query_df)} 条，目标数据: {len(target_df)} 条")

        # 对于负样本，使用更严格的阈值
        if is_negative_sample:
            threshold = threshold * 1.5  # 提高阈值，减少假正例
            logger.info(f"负样本检测模式，阈值提高到: {threshold}")

        # 预处理目标数据
        if progress_callback:
            progress_callback(5)

        target_clean = target_df.copy()
        target_clean['name_upper'] = target_clean[target_col].str.upper().str.strip()
        target_clean['name_length'] = target_clean['name_upper'].str.len()

        # 创建快速查找字典
        target_dict = {}
        for idx, row in target_clean.iterrows():
            name = row['name_upper']
            target_dict[name] = {
                'id': row['ID'],
                'original': row[target_col],
                'length': row['name_length']
            }

        if progress_callback:
            progress_callback(15)

        matches = []
        total_queries = len(query_df)

        logger.info("开始快速匹配...")

        # 批量处理优化
        batch_size = 1000  # 增大批量大小
        for batch_start in range(0, total_queries, batch_size):
            batch_end = min(batch_start + batch_size, total_queries)
            batch_df = query_df.iloc[batch_start:batch_end]

            # 批量预处理
            batch_queries = []
            for i, (idx, query_row) in enumerate(batch_df.iterrows()):
                query_id = query_row['ID']
                query_text = str(query_row[query_col])
                query_upper = query_text.upper().strip()
                batch_queries.append((query_id, query_text, query_upper))

            # 批量匹配
            for query_id, query_text, query_upper in batch_queries:
                # 策略1: 完全相同匹配
                if query_upper in target_dict:
                    matches.append({
                        'query_id': query_id,
                        'query_name': query_text,
                        'matched_id': target_dict[query_upper]['id'],
                        'matched_name': target_dict[query_upper]['original'],
                        'similarity_score': 1.0,
                        'match_method': 'exact'
                    })
                else:
                    # 策略2: 智能模糊匹配
                    best_match = self._simple_fuzzy_match(query_upper, target_dict, threshold)
                    if best_match:
                        matches.append({
                            'query_id': query_id,
                            'query_name': query_text,
                            'matched_id': best_match['id'],
                            'matched_name': best_match['original'],
                            'similarity_score': best_match['score'],
                            'match_method': 'fuzzy'
                        })

            # 更新进度
            if progress_callback:
                progress = 15 + int((batch_end / total_queries) * 80)
                progress_callback(progress)

        if progress_callback:
            progress_callback(100)

        result_df = pd.DataFrame(matches)
        logger.info(f"超高速匹配完成，找到 {len(result_df)} 个匹配")

        return result_df

    def _simple_fuzzy_match(self, query_upper: str, target_dict: dict, threshold: float) -> dict:
        """
        智能快速的模糊匹配 - 优化版
        """
        best_score = 0.0
        best_match = None
        query_len = len(query_upper)

        # 智能候选筛选策略
        candidates = self._smart_candidate_selection(query_upper, target_dict, query_len)

        # 限制候选数量以保持速度
        if len(candidates) > 800:
            candidates = candidates[:800]

        for target_name, target_info in candidates:
            # 简单相似度计算
            score = self._simple_similarity(query_upper, target_name)

            if score > best_score and score >= threshold:
                best_score = score
                best_match = {
                    'id': target_info['id'],
                    'original': target_info['original'],
                    'score': score
                }

                # 早期终止
                if score > 0.9:
                    break

        return best_match

    def _smart_candidate_selection(self, query_upper: str, target_dict: dict, query_len: int) -> list:
        """
        智能候选选择 - 优先选择可能匹配的候选
        """
        candidates = []
        query_first_word = query_upper.split()[0] if query_upper.split() else ""
        query_first_char = query_upper[0] if query_upper else ""

        # 策略1: 长度相似的候选 (优先级最高)
        length_candidates = []
        first_char_candidates = []
        first_word_candidates = []
        other_candidates = []

        for target_name, target_info in target_dict.items():
            target_len = target_info['length']

            # 长度预筛选 (更宽松的范围)
            if abs(query_len - target_len) > max(query_len, target_len) * 0.6:
                continue

            # 分类候选
            if abs(query_len - target_len) <= max(query_len, target_len) * 0.2:
                length_candidates.append((target_name, target_info))
            elif target_name.startswith(query_first_char):
                first_char_candidates.append((target_name, target_info))
            elif query_first_word and target_name.startswith(query_first_word):
                first_word_candidates.append((target_name, target_info))
            else:
                other_candidates.append((target_name, target_info))

        # 按优先级合并候选
        candidates.extend(length_candidates[:300])  # 长度相似的优先
        candidates.extend(first_word_candidates[:200])  # 首词匹配次之
        candidates.extend(first_char_candidates[:200])  # 首字符匹配再次
        candidates.extend(other_candidates[:100])  # 其他候选最后

        return candidates

    def _simple_similarity(self, str1: str, str2: str) -> float:
        """
        超简单的相似度计算，支持首字母缩写匹配 - 优化版
        """
        # 快速完全匹配检查
        if str1 == str2:
            return 1.0

        # 快速长度检查 - 如果长度差异太大，直接返回0
        len1, len2 = len(str1), len(str2)
        if abs(len1 - len2) > max(len1, len2) * 0.7:
            return 0.0

        # 包含关系检查 (优化：先检查较短的字符串)
        if len1 < len2:
            if str1 in str2:
                return 0.8
        else:
            if str2 in str1:
                return 0.8

        # 首字母缩写匹配检测
        initial_score = self._check_initial_match(str1, str2)
        if initial_score > 0:
            return initial_score

        # 优化的字符重叠率计算
        return self._fast_char_overlap(str1, str2)

    def _fast_char_overlap(self, str1: str, str2: str) -> float:
        """
        快速字符重叠率计算
        """
        # 移除空格并转为集合
        chars1 = set(str1.replace(' ', ''))
        chars2 = set(str2.replace(' ', ''))

        if not chars1 or not chars2:
            return 0.0

        # 快速计算交集和并集
        intersection_size = len(chars1 & chars2)
        union_size = len(chars1) + len(chars2) - intersection_size

        return intersection_size / union_size if union_size > 0 else 0.0

    def _check_initial_match(self, str1: str, str2: str) -> float:
        """
        检查首字母缩写匹配
        """
        # 分割姓名
        parts1 = str1.replace(',', ' ').split()
        parts2 = str2.replace(',', ' ').split()

        if len(parts1) < 2 or len(parts2) < 2:
            return 0.0

        # 检查姓氏是否匹配
        surname1 = parts1[0].upper()
        surname2 = parts2[0].upper()

        if surname1 != surname2:
            return 0.0

        # 检查名字部分的首字母匹配
        given_names1 = parts1[1:]
        given_names2 = parts2[1:]

        # 提取首字母
        initials1 = [name[0].upper() for name in given_names1 if name]
        initials2 = []

        for name in given_names2:
            if len(name) == 1 or (len(name) == 2 and name.endswith('.')):
                # 这是一个首字母
                initials2.append(name[0].upper())
            else:
                # 这是完整名字，提取首字母
                initials2.append(name[0].upper())

        # 计算首字母匹配度
        if not initials1 or not initials2:
            return 0.0

        matches = 0
        for i2 in initials2:
            if i2 in initials1:
                matches += 1

        # 如果大部分首字母匹配，给高分
        match_ratio = matches / max(len(initials1), len(initials2))

        if match_ratio >= 0.5:  # 至少50%的首字母匹配
            return 0.75  # 给一个较高的分数

        return 0.0

    def _build_target_index(self, target_df: pd.DataFrame, target_col: str) -> Dict:
        """构建目标数据的高效索引"""
        logger.info("构建高效索引...")

        index = {
            'by_length': defaultdict(list),
            'by_first_char': defaultdict(list),
            'by_first_word': defaultdict(list),
            'all_records': []
        }

        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row[target_col])

            # 预处理文本
            processed_text = self.preprocessor.normalize_text(target_text)

            record = {
                'id': target_id,
                'original': target_text,
                'processed': processed_text,
                'length': len(processed_text),
                'first_char': processed_text[0] if processed_text else '',
                'first_word': processed_text.split()[0] if processed_text.split() else ''
            }

            # 添加到各种索引
            index['by_length'][record['length']].append(record)
            index['by_first_char'][record['first_char']].append(record)
            index['by_first_word'][record['first_word']].append(record)
            index['all_records'].append(record)

        logger.info(f"索引构建完成，共 {len(index['all_records'])} 条记录")
        return index

    def _ultra_fast_process_batch(self, batch_df: pd.DataFrame, target_index: Dict,
                                 threshold: float, method: str, query_col: str) -> List[Dict]:
        """超快速批量处理"""
        batch_matches = []

        for idx, query_row in batch_df.iterrows():
            query_id = query_row['ID']
            query_text = str(query_row[query_col])

            # 超快速匹配
            best_match = self._ultra_fast_find_best_match(
                query_text, target_index, threshold, method
            )

            if best_match is not None:
                batch_matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method
                })

        return batch_matches

    def _ultra_fast_find_best_match(self, query_text: str, target_index: Dict,
                                   threshold: float, method: str) -> Optional[Dict]:
        """超快速查找最佳匹配"""
        best_score = 0.0
        best_match = None

        # 预处理查询文本
        processed_query = self.preprocessor.normalize_text(query_text)
        query_length = len(processed_query)
        query_first_char = processed_query[0] if processed_query else ''
        query_first_word = processed_query.split()[0] if processed_query.split() else ''

        # 策略1: 首先尝试精确匹配
        for record in target_index['all_records']:
            if record['processed'] == processed_query:
                return {
                    'id': record['id'],
                    'name': record['original'],
                    'score': 1.0
                }

        # 策略2: 长度预筛选 (长度差异不超过30%)
        candidate_records = []
        min_length = int(query_length * 0.7)
        max_length = int(query_length * 1.3)

        for length in range(min_length, max_length + 1):
            candidate_records.extend(target_index['by_length'].get(length, []))

        # 策略3: 如果候选太多，进一步筛选
        if len(candidate_records) > 1000:
            # 使用首字符筛选
            first_char_candidates = target_index['by_first_char'].get(query_first_char, [])
            if first_char_candidates:
                candidate_records = [r for r in candidate_records if r in first_char_candidates]

        # 策略4: 如果还是太多，使用首词筛选
        if len(candidate_records) > 500:
            first_word_candidates = target_index['by_first_word'].get(query_first_word, [])
            if first_word_candidates:
                candidate_records = [r for r in candidate_records if r in first_word_candidates]

        # 策略5: 限制最大候选数量
        if len(candidate_records) > 200:
            candidate_records = candidate_records[:200]

        # 计算相似度
        for record in candidate_records:
            score = self._calculate_similarity(processed_query, record['processed'], method)

            # 早期终止：如果找到很高的分数，直接返回
            if score > 0.95:
                return {
                    'id': record['id'],
                    'name': record['original'],
                    'score': score
                }

            # 更新最佳匹配
            if score > best_score and score >= threshold:
                best_score = score
                best_match = {
                    'id': record['id'],
                    'name': record['original'],
                    'score': score
                }

        return best_match
    
    def _find_best_match(self, 
                        query_text: str, 
                        target_df: pd.DataFrame, 
                        target_col: str,
                        threshold: float, 
                        method: str) -> Optional[Dict]:
        """
        找到最佳匹配
        
        Args:
            query_text: 查询文本
            target_df: 目标数据集
            target_col: 目标列名
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            Dict: 最佳匹配信息，如果没有找到则返回None
        """
        best_score = 0.0
        best_match = None
        
        # 预处理查询文本
        processed_query = self.preprocessor.normalize_text(query_text)
        
        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row[target_col])
            
            # 预处理目标文本
            processed_target = self.preprocessor.normalize_text(target_text)
            
            # 计算相似度
            if method == 'levenshtein':
                score = self.similarity_calc.levenshtein_similarity(processed_query, processed_target)
            elif method == 'jaro_winkler':
                score = self.similarity_calc.jaro_winkler_similarity(processed_query, processed_target)
            elif method == 'jaccard':
                score = self.similarity_calc.jaccard_token_similarity(processed_query, processed_target)
            elif method == 'combined':
                score = self.similarity_calc.combined_similarity(processed_query, processed_target)
            else:
                score = self.similarity_calc.combined_similarity(processed_query, processed_target)
            
            # 更新最佳匹配
            if score > best_score and score >= threshold:
                best_score = score
                best_match = {
                    'id': target_id,
                    'name': target_text,
                    'score': score
                }
        
        return best_match
    
    def match_with_preprocessing(self, 
                               query_df: pd.DataFrame, 
                               target_df: pd.DataFrame,
                               sheet_type: str = None,
                               threshold: float = 0.7,
                               method: str = 'combined') -> pd.DataFrame:
        """
        带预处理的实体匹配
        
        Args:
            query_df: 查询数据集
            target_df: 目标数据集
            sheet_type: 数据表类型
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            pd.DataFrame: 匹配结果
        """
        logger.info(f"开始带预处理的实体匹配，sheet类型: {sheet_type}")
        
        matches = []
        
        for idx, query_row in query_df.iterrows():
            query_id = query_row['ID']
            query_text = str(query_row['NAME'])
            
            # 根据sheet类型预处理查询文本
            if sheet_type:
                processed_query = self.preprocessor.preprocess_by_sheet_type(query_text, sheet_type)
            else:
                processed_query = self.preprocessor.normalize_text(query_text)
            
            best_match = self._find_best_match_with_variants(
                query_text,
                processed_query,
                target_df,
                threshold,
                method,
                sheet_type
            )
            
            if best_match is not None:
                matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method,
                    'preprocessing': sheet_type or 'standard'
                })
        
        result_df = pd.DataFrame(matches)
        logger.info(f"匹配完成，找到 {len(result_df)} 个匹配")
        
        return result_df
    
    def _find_best_match_with_variants(self, 
                                     original_query: str,
                                     processed_query: str,
                                     target_df: pd.DataFrame,
                                     threshold: float,
                                     method: str,
                                     sheet_type: str = None) -> Optional[Dict]:
        """
        使用多种变体找到最佳匹配
        
        Args:
            original_query: 原始查询文本
            processed_query: 预处理后的查询文本
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            sheet_type: 数据表类型
            
        Returns:
            Dict: 最佳匹配信息
        """
        best_score = 0.0
        best_match = None
        
        # 创建查询变体
        query_variants = self.preprocessor.create_search_variants(original_query)
        if processed_query not in query_variants:
            query_variants.append(processed_query)
        
        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row['NAME'])
            
            # 创建目标变体
            target_variants = self.preprocessor.create_search_variants(target_text)
            
            # 计算所有变体组合的相似度
            max_score = 0.0
            for query_var in query_variants:
                for target_var in target_variants:
                    if sheet_type:
                        score = self.similarity_calc.adaptive_similarity(query_var, target_var, sheet_type)
                    else:
                        if method == 'levenshtein':
                            score = self.similarity_calc.levenshtein_similarity(query_var, target_var)
                        elif method == 'jaro_winkler':
                            score = self.similarity_calc.jaro_winkler_similarity(query_var, target_var)
                        elif method == 'jaccard':
                            score = self.similarity_calc.jaccard_token_similarity(query_var, target_var)
                        else:
                            score = self.similarity_calc.combined_similarity(query_var, target_var)
                    
                    max_score = max(max_score, score)
            
            # 更新最佳匹配
            if max_score > best_score and max_score >= threshold:
                best_score = max_score
                best_match = {
                    'id': target_id,
                    'name': target_text,
                    'score': max_score
                }
        
        return best_match
    
    def batch_match_test02_sheets(self, 
                                 test02_sheets: Dict[str, pd.DataFrame],
                                 target_df: pd.DataFrame,
                                 threshold: float = 0.7,
                                 method: str = 'combined') -> Dict[str, pd.DataFrame]:
        """
        批量匹配test02的所有sheet
        
        Args:
            test02_sheets: test02的所有sheet数据
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            Dict[str, pd.DataFrame]: 各sheet的匹配结果
        """
        results = {}
        
        for sheet_name, sheet_df in test02_sheets.items():
            if sheet_name == 'Desc':
                continue
            
            logger.info(f"匹配 {sheet_name}...")
            
            # 对Sheet8使用特殊处理（负样本）
            is_negative = (sheet_name == 'Sheet8')
            
            if is_negative:
                # 负样本使用更严格的阈值和方法
                result_df = self.match_entities(
                    sheet_df,
                    target_df,
                    threshold=threshold * 1.5,  # 更严格的阈值
                    method=method,
                    is_negative_sample=True
                )
            else:
                # 正常匹配
                result_df = self.match_with_preprocessing(
                    sheet_df,
                    target_df,
                    sheet_type=sheet_name,
                    threshold=threshold,
                    method=method
                )
            
            results[sheet_name] = result_df
        
        return results
    
    def get_match_statistics(self, match_results: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        获取匹配统计信息
        
        Args:
            match_results: 匹配结果
            
        Returns:
            Dict: 统计信息
        """
        statistics = {}
        
        for sheet_name, result_df in match_results.items():
            stats = {
                'total_matches': len(result_df),
                'avg_similarity': result_df['similarity_score'].mean() if len(result_df) > 0 else 0.0,
                'min_similarity': result_df['similarity_score'].min() if len(result_df) > 0 else 0.0,
                'max_similarity': result_df['similarity_score'].max() if len(result_df) > 0 else 0.0,
                'similarity_distribution': {
                    '0.9+': len(result_df[result_df['similarity_score'] >= 0.9]),
                    '0.8-0.9': len(result_df[(result_df['similarity_score'] >= 0.8) & 
                                           (result_df['similarity_score'] < 0.9)]),
                    '0.7-0.8': len(result_df[(result_df['similarity_score'] >= 0.7) & 
                                           (result_df['similarity_score'] < 0.8)]),
                    '<0.7': len(result_df[result_df['similarity_score'] < 0.7])
                }
            }
            statistics[sheet_name] = stats
        
        return statistics
