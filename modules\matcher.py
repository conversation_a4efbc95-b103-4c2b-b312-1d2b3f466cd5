#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实体匹配模块
Entity Matching Module
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from .preprocessor import TextPreprocessor
from .similarity_calculator import SimilarityCalculator

logger = logging.getLogger(__name__)

class EntityMatcher:
    """实体匹配器类"""
    
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        self.similarity_calc = SimilarityCalculator()
    
    def match_entities(self,
                      query_df: pd.DataFrame,
                      target_df: pd.DataFrame,
                      threshold: float = 0.7,
                      method: str = 'combined',
                      query_col: str = 'NAME',
                      target_col: str = 'NAME',
                      is_negative_sample: bool = False,
                      progress_callback=None) -> pd.DataFrame:
        """
        执行实体匹配
        
        Args:
            query_df: 查询数据集
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            query_col: 查询列名
            target_col: 目标列名
            is_negative_sample: 是否为负样本
            
        Returns:
            pd.DataFrame: 匹配结果
        """
        logger.info(f"开始实体匹配，查询数据: {len(query_df)} 条，目标数据: {len(target_df)} 条")
        
        matches = []
        total_queries = len(query_df)

        for i, (idx, query_row) in enumerate(query_df.iterrows()):
            # 更新进度
            if progress_callback and i % 100 == 0:
                progress = int((i / total_queries) * 100)
                progress_callback(progress)

            query_id = query_row['ID']
            query_text = str(query_row[query_col])

            # 对于负样本，使用更严格的阈值
            current_threshold = threshold * 1.2 if is_negative_sample else threshold

            best_match = self._find_best_match(
                query_text,
                target_df,
                target_col,
                current_threshold,
                method
            )

            if best_match is not None:
                matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method
                })

        # 最终进度更新
        if progress_callback:
            progress_callback(100)
        
        result_df = pd.DataFrame(matches)
        logger.info(f"匹配完成，找到 {len(result_df)} 个匹配")
        
        return result_df
    
    def _find_best_match(self, 
                        query_text: str, 
                        target_df: pd.DataFrame, 
                        target_col: str,
                        threshold: float, 
                        method: str) -> Optional[Dict]:
        """
        找到最佳匹配
        
        Args:
            query_text: 查询文本
            target_df: 目标数据集
            target_col: 目标列名
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            Dict: 最佳匹配信息，如果没有找到则返回None
        """
        best_score = 0.0
        best_match = None
        
        # 预处理查询文本
        processed_query = self.preprocessor.normalize_text(query_text)
        
        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row[target_col])
            
            # 预处理目标文本
            processed_target = self.preprocessor.normalize_text(target_text)
            
            # 计算相似度
            if method == 'levenshtein':
                score = self.similarity_calc.levenshtein_similarity(processed_query, processed_target)
            elif method == 'jaro_winkler':
                score = self.similarity_calc.jaro_winkler_similarity(processed_query, processed_target)
            elif method == 'jaccard':
                score = self.similarity_calc.jaccard_token_similarity(processed_query, processed_target)
            elif method == 'combined':
                score = self.similarity_calc.combined_similarity(processed_query, processed_target)
            else:
                score = self.similarity_calc.combined_similarity(processed_query, processed_target)
            
            # 更新最佳匹配
            if score > best_score and score >= threshold:
                best_score = score
                best_match = {
                    'id': target_id,
                    'name': target_text,
                    'score': score
                }
        
        return best_match
    
    def match_with_preprocessing(self, 
                               query_df: pd.DataFrame, 
                               target_df: pd.DataFrame,
                               sheet_type: str = None,
                               threshold: float = 0.7,
                               method: str = 'combined') -> pd.DataFrame:
        """
        带预处理的实体匹配
        
        Args:
            query_df: 查询数据集
            target_df: 目标数据集
            sheet_type: 数据表类型
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            pd.DataFrame: 匹配结果
        """
        logger.info(f"开始带预处理的实体匹配，sheet类型: {sheet_type}")
        
        matches = []
        
        for idx, query_row in query_df.iterrows():
            query_id = query_row['ID']
            query_text = str(query_row['NAME'])
            
            # 根据sheet类型预处理查询文本
            if sheet_type:
                processed_query = self.preprocessor.preprocess_by_sheet_type(query_text, sheet_type)
            else:
                processed_query = self.preprocessor.normalize_text(query_text)
            
            best_match = self._find_best_match_with_variants(
                query_text,
                processed_query,
                target_df,
                threshold,
                method,
                sheet_type
            )
            
            if best_match is not None:
                matches.append({
                    'query_id': query_id,
                    'query_name': query_text,
                    'matched_id': best_match['id'],
                    'matched_name': best_match['name'],
                    'similarity_score': best_match['score'],
                    'match_method': method,
                    'preprocessing': sheet_type or 'standard'
                })
        
        result_df = pd.DataFrame(matches)
        logger.info(f"匹配完成，找到 {len(result_df)} 个匹配")
        
        return result_df
    
    def _find_best_match_with_variants(self, 
                                     original_query: str,
                                     processed_query: str,
                                     target_df: pd.DataFrame,
                                     threshold: float,
                                     method: str,
                                     sheet_type: str = None) -> Optional[Dict]:
        """
        使用多种变体找到最佳匹配
        
        Args:
            original_query: 原始查询文本
            processed_query: 预处理后的查询文本
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            sheet_type: 数据表类型
            
        Returns:
            Dict: 最佳匹配信息
        """
        best_score = 0.0
        best_match = None
        
        # 创建查询变体
        query_variants = self.preprocessor.create_search_variants(original_query)
        if processed_query not in query_variants:
            query_variants.append(processed_query)
        
        for idx, target_row in target_df.iterrows():
            target_id = target_row['ID']
            target_text = str(target_row['NAME'])
            
            # 创建目标变体
            target_variants = self.preprocessor.create_search_variants(target_text)
            
            # 计算所有变体组合的相似度
            max_score = 0.0
            for query_var in query_variants:
                for target_var in target_variants:
                    if sheet_type:
                        score = self.similarity_calc.adaptive_similarity(query_var, target_var, sheet_type)
                    else:
                        if method == 'levenshtein':
                            score = self.similarity_calc.levenshtein_similarity(query_var, target_var)
                        elif method == 'jaro_winkler':
                            score = self.similarity_calc.jaro_winkler_similarity(query_var, target_var)
                        elif method == 'jaccard':
                            score = self.similarity_calc.jaccard_token_similarity(query_var, target_var)
                        else:
                            score = self.similarity_calc.combined_similarity(query_var, target_var)
                    
                    max_score = max(max_score, score)
            
            # 更新最佳匹配
            if max_score > best_score and max_score >= threshold:
                best_score = max_score
                best_match = {
                    'id': target_id,
                    'name': target_text,
                    'score': max_score
                }
        
        return best_match
    
    def batch_match_test02_sheets(self, 
                                 test02_sheets: Dict[str, pd.DataFrame],
                                 target_df: pd.DataFrame,
                                 threshold: float = 0.7,
                                 method: str = 'combined') -> Dict[str, pd.DataFrame]:
        """
        批量匹配test02的所有sheet
        
        Args:
            test02_sheets: test02的所有sheet数据
            target_df: 目标数据集
            threshold: 相似度阈值
            method: 匹配方法
            
        Returns:
            Dict[str, pd.DataFrame]: 各sheet的匹配结果
        """
        results = {}
        
        for sheet_name, sheet_df in test02_sheets.items():
            if sheet_name == 'Desc':
                continue
            
            logger.info(f"匹配 {sheet_name}...")
            
            # 对Sheet8使用特殊处理（负样本）
            is_negative = (sheet_name == 'Sheet8')
            
            if is_negative:
                # 负样本使用更严格的阈值和方法
                result_df = self.match_entities(
                    sheet_df,
                    target_df,
                    threshold=threshold * 1.5,  # 更严格的阈值
                    method=method,
                    is_negative_sample=True
                )
            else:
                # 正常匹配
                result_df = self.match_with_preprocessing(
                    sheet_df,
                    target_df,
                    sheet_type=sheet_name,
                    threshold=threshold,
                    method=method
                )
            
            results[sheet_name] = result_df
        
        return results
    
    def get_match_statistics(self, match_results: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        获取匹配统计信息
        
        Args:
            match_results: 匹配结果
            
        Returns:
            Dict: 统计信息
        """
        statistics = {}
        
        for sheet_name, result_df in match_results.items():
            stats = {
                'total_matches': len(result_df),
                'avg_similarity': result_df['similarity_score'].mean() if len(result_df) > 0 else 0.0,
                'min_similarity': result_df['similarity_score'].min() if len(result_df) > 0 else 0.0,
                'max_similarity': result_df['similarity_score'].max() if len(result_df) > 0 else 0.0,
                'similarity_distribution': {
                    '0.9+': len(result_df[result_df['similarity_score'] >= 0.9]),
                    '0.8-0.9': len(result_df[(result_df['similarity_score'] >= 0.8) & 
                                           (result_df['similarity_score'] < 0.9)]),
                    '0.7-0.8': len(result_df[(result_df['similarity_score'] >= 0.7) & 
                                           (result_df['similarity_score'] < 0.8)]),
                    '<0.7': len(result_df[result_df['similarity_score'] < 0.7])
                }
            }
            statistics[sheet_name] = stats
        
        return statistics
