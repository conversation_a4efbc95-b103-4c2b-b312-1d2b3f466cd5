#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载模块
Data Loading Module
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        self.loaded_data = {}
    
    def load_primary_data(self, file_path: str) -> pd.DataFrame:
        """
        加载primary.csv数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            pd.DataFrame: 加载的数据
        """
        try:
            logger.info(f"加载primary数据: {file_path}")
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 数据验证
            required_columns = ['ID', 'NAME', 'TYPE']
            if not all(col in df.columns for col in required_columns):
                raise ValueError(f"Primary数据缺少必需列: {required_columns}")
            
            # 数据清理
            df = df.dropna(subset=['ID', 'NAME'])
            df['ID'] = df['ID'].astype(int)
            df['NAME'] = df['NAME'].astype(str).str.strip()
            df['TYPE'] = df['TYPE'].astype(str).str.strip()
            
            logger.info(f"Primary数据加载完成，共 {len(df)} 条记录")
            self.loaded_data['primary'] = df
            return df
            
        except Exception as e:
            logger.error(f"加载primary数据失败: {str(e)}")
            raise
    
    def load_alternate_data(self, file_path: str) -> pd.DataFrame:
        """
        加载alternate.csv数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            pd.DataFrame: 加载的数据
        """
        try:
            logger.info(f"加载alternate数据: {file_path}")
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 数据验证
            required_columns = ['ID', 'NAME']
            if not all(col in df.columns for col in required_columns):
                raise ValueError(f"Alternate数据缺少必需列: {required_columns}")
            
            # 数据清理
            df = df.dropna(subset=['ID', 'NAME'])
            df['ID'] = df['ID'].astype(int)
            df['NAME'] = df['NAME'].astype(str).str.strip()
            
            logger.info(f"Alternate数据加载完成，共 {len(df)} 条记录")
            self.loaded_data['alternate'] = df
            return df
            
        except Exception as e:
            logger.error(f"加载alternate数据失败: {str(e)}")
            raise
    
    def load_test_01_data(self, file_path: str) -> pd.DataFrame:
        """
        加载test_01.csv数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            pd.DataFrame: 加载的数据
        """
        try:
            logger.info(f"加载test_01数据: {file_path}")
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 数据验证
            required_columns = ['ID', 'VARIANT']
            if not all(col in df.columns for col in required_columns):
                raise ValueError(f"Test_01数据缺少必需列: {required_columns}")
            
            # 数据清理
            df = df.dropna(subset=['ID', 'VARIANT'])
            df['ID'] = df['ID'].astype(int)
            df['VARIANT'] = df['VARIANT'].astype(str).str.strip()
            
            logger.info(f"Test_01数据加载完成，共 {len(df)} 条记录")
            self.loaded_data['test_01'] = df
            return df
            
        except Exception as e:
            logger.error(f"加载test_01数据失败: {str(e)}")
            raise
    
    def load_test_02_data(self, file_path: str) -> Dict[str, pd.DataFrame]:
        """
        加载test_02.xlsx数据（多个sheet）
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, pd.DataFrame]: 各sheet的数据字典
        """
        try:
            logger.info(f"加载test_02数据: {file_path}")
            
            # 读取所有sheet
            xl_file = pd.ExcelFile(file_path)
            sheets_data = {}
            
            for sheet_name in xl_file.sheet_names:
                logger.info(f"加载sheet: {sheet_name}")
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                if sheet_name == 'Desc':
                    # 描述sheet，保持原样
                    sheets_data[sheet_name] = df
                else:
                    # 数据sheet，进行清理
                    required_columns = ['ID', 'NAME']
                    if not all(col in df.columns for col in required_columns):
                        logger.warning(f"Sheet {sheet_name} 缺少必需列: {required_columns}")
                        continue
                    
                    # 数据清理
                    df = df.dropna(subset=['ID', 'NAME'])
                    df['ID'] = df['ID'].astype(int)
                    df['NAME'] = df['NAME'].astype(str).str.strip()
                    
                    sheets_data[sheet_name] = df
                    logger.info(f"Sheet {sheet_name} 加载完成，共 {len(df)} 条记录")
            
            logger.info(f"Test_02数据加载完成，共 {len(sheets_data)} 个sheet")
            self.loaded_data['test_02'] = sheets_data
            return sheets_data
            
        except Exception as e:
            logger.error(f"加载test_02数据失败: {str(e)}")
            raise
    
    def get_data_summary(self) -> Dict:
        """
        获取已加载数据的摘要信息
        
        Returns:
            Dict: 数据摘要
        """
        summary = {}
        
        if 'primary' in self.loaded_data:
            df = self.loaded_data['primary']
            summary['primary'] = {
                'count': len(df),
                'columns': list(df.columns),
                'sample': df.head(3).to_dict('records') if len(df) > 0 else []
            }
        
        if 'alternate' in self.loaded_data:
            df = self.loaded_data['alternate']
            summary['alternate'] = {
                'count': len(df),
                'columns': list(df.columns),
                'sample': df.head(3).to_dict('records') if len(df) > 0 else []
            }
        
        if 'test_01' in self.loaded_data:
            df = self.loaded_data['test_01']
            summary['test_01'] = {
                'count': len(df),
                'columns': list(df.columns),
                'sample': df.head(3).to_dict('records') if len(df) > 0 else []
            }
        
        if 'test_02' in self.loaded_data:
            sheets_summary = {}
            for sheet_name, df in self.loaded_data['test_02'].items():
                sheets_summary[sheet_name] = {
                    'count': len(df),
                    'columns': list(df.columns),
                    'sample': df.head(3).to_dict('records') if len(df) > 0 else []
                }
            summary['test_02'] = sheets_summary
        
        return summary
    
    def validate_data_integrity(self) -> Dict[str, bool]:
        """
        验证数据完整性
        
        Returns:
            Dict[str, bool]: 各数据集的验证结果
        """
        validation_results = {}
        
        # 验证primary数据
        if 'primary' in self.loaded_data:
            df = self.loaded_data['primary']
            validation_results['primary'] = (
                len(df) > 0 and
                'ID' in df.columns and
                'NAME' in df.columns and
                df['ID'].notna().all() and
                df['NAME'].notna().all()
            )
        
        # 验证alternate数据
        if 'alternate' in self.loaded_data:
            df = self.loaded_data['alternate']
            validation_results['alternate'] = (
                len(df) > 0 and
                'ID' in df.columns and
                'NAME' in df.columns and
                df['ID'].notna().all() and
                df['NAME'].notna().all()
            )
        
        # 验证test_01数据
        if 'test_01' in self.loaded_data:
            df = self.loaded_data['test_01']
            validation_results['test_01'] = (
                len(df) > 0 and
                'ID' in df.columns and
                'VARIANT' in df.columns and
                df['ID'].notna().all() and
                df['VARIANT'].notna().all()
            )
        
        # 验证test_02数据
        if 'test_02' in self.loaded_data:
            all_sheets_valid = True
            for sheet_name, df in self.loaded_data['test_02'].items():
                if sheet_name == 'Desc':
                    continue
                sheet_valid = (
                    len(df) > 0 and
                    'ID' in df.columns and
                    'NAME' in df.columns and
                    df['ID'].notna().all() and
                    df['NAME'].notna().all()
                )
                if not sheet_valid:
                    all_sheets_valid = False
                    break
            validation_results['test_02'] = all_sheets_valid
        
        return validation_results
