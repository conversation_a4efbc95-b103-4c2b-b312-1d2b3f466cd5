{% extends "base.html" %}

{% block title %}数据加载 - 实体匹配与去重系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据加载与验证
                </h4>
            </div>
            <div class="card-body">
                {% if not loaded %}
                <!-- 数据加载表单 -->
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-upload me-2"></i>
                            加载数据文件
                        </h5>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>说明：</strong>系统将自动从DATA目录加载以下文件：
                            <ul class="mb-0 mt-2">
                                <li>primary.csv - 标准实体数据</li>
                                <li>alternate.csv - 实体变体数据</li>
                                <li>test_01.csv - 测试匹配数据</li>
                                <li>test_02.xlsx - 变换测试数据（8个sheet）</li>
                            </ul>
                        </div>
                        
                        <form method="POST" id="loadDataForm">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="loadButton">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span class="button-text">
                                        <i class="fas fa-database me-2"></i>
                                        开始加载数据
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    数据要求
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-file-csv text-primary me-2"></i>CSV文件需UTF-8编码</li>
                                    <li><i class="fas fa-file-excel text-success me-2"></i>Excel文件需包含指定sheet</li>
                                    <li><i class="fas fa-columns text-info me-2"></i>必需列：ID, NAME</li>
                                    <li><i class="fas fa-shield-alt text-warning me-2"></i>数据完整性验证</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card bg-light mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    预计时间
                                </h6>
                                <p class="small mb-0">
                                    数据加载通常需要10-30秒，具体时间取决于文件大小和系统性能。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <!-- 数据加载成功 -->
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>数据加载成功！</strong>所有数据文件已成功加载并验证。
                </div>
                
                <!-- 数据统计 -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ "{:,}".format(stats.primary_count) }}</div>
                            <div class="metric-label">Primary 记录数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ "{:,}".format(stats.alternate_count) }}</div>
                            <div class="metric-label">Alternate 记录数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ "{:,}".format(stats.test_01_count) }}</div>
                            <div class="metric-label">Test_01 记录数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value">{{ stats.test_02_sheets|length - 1 }}</div>
                            <div class="metric-label">Test_02 Sheet数</div>
                        </div>
                    </div>
                </div>
                
                <!-- Test_02 Sheet详情 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Test_02 Sheet详情
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sheet名称</th>
                                        <th>记录数</th>
                                        <th>数据变换类型</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sheet_name, count in stats.test_02_sheets.items() %}
                                    {% if sheet_name != 'Desc' %}
                                    <tr>
                                        <td><strong>{{ sheet_name }}</strong></td>
                                        <td>{{ "{:,}".format(count) }}</td>
                                        <td>
                                            {% if sheet_name == 'Sheet1' %}
                                                <span class="badge bg-primary">移除特殊字符</span>
                                            {% elif sheet_name == 'Sheet2' %}
                                                <span class="badge bg-success">单词打乱</span>
                                            {% elif sheet_name == 'Sheet3' %}
                                                <span class="badge bg-info">字符分离</span>
                                            {% elif sheet_name == 'Sheet4' %}
                                                <span class="badge bg-warning">组合变换</span>
                                            {% elif sheet_name == 'Sheet5' %}
                                                <span class="badge bg-secondary">单词移除</span>
                                            {% elif sheet_name == 'Sheet6' %}
                                                <span class="badge bg-dark">单词截断</span>
                                            {% elif sheet_name == 'Sheet7' %}
                                                <span class="badge bg-light text-dark">待分析</span>
                                            {% elif sheet_name == 'Sheet8' %}
                                                <span class="badge bg-danger">负样本</span>
                                            {% endif %}
                                        </td>
                                        <td class="small">
                                            {% if sheet_name == 'Sheet1' %}
                                                移除所有特殊字符和空格
                                            {% elif sheet_name == 'Sheet2' %}
                                                打乱单词顺序然后移除特殊字符
                                            {% elif sheet_name == 'Sheet3' %}
                                                每个字符之间用空格分隔
                                            {% elif sheet_name == 'Sheet4' %}
                                                打乱单词顺序然后字符分离
                                            {% elif sheet_name == 'Sheet5' %}
                                                移除部分单词
                                            {% elif sheet_name == 'Sheet6' %}
                                                截断单词长度
                                            {% elif sheet_name == 'Sheet7' %}
                                                需要进一步分析的变换
                                            {% elif sheet_name == 'Sheet8' %}
                                                无真实匹配的负样本
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 下一步操作 -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-filter text-success me-2"></i>
                                    去重处理
                                </h5>
                                <p class="card-text">
                                    对primary和alternate数据进行智能去重处理
                                </p>
                                <a href="{{ url_for('deduplication') }}" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    开始去重
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-search text-primary me-2"></i>
                                    实体匹配
                                </h5>
                                <p class="card-text">
                                    直接进行实体匹配（跳过去重步骤）
                                </p>
                                <a href="{{ url_for('matching') }}" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    开始匹配
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 数据预览模态框 -->
<div class="modal fade" id="dataPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">数据预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="dataPreviewContent">
                    <!-- 数据预览内容将通过JavaScript加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('loadDataForm');
        const button = document.getElementById('loadButton');
        
        if (form && button) {
            form.addEventListener('submit', function(e) {
                showLoading('loadButton');
            });
        }
        
        // 添加进度条动画
        {% if loaded %}
        // 数据加载成功后的动画效果
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            }, index * 100);
        });
        
        // 数字计数动画
        const metricValues = document.querySelectorAll('.metric-value');
        metricValues.forEach(element => {
            const finalValue = parseInt(element.textContent.replace(/,/g, ''));
            let currentValue = 0;
            const increment = finalValue / 50;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue).toLocaleString();
            }, 30);
        });
        {% endif %}
    });
    
    // 数据预览功能
    function previewData(dataType) {
        // 这里可以添加AJAX请求来获取数据预览
        const modal = new bootstrap.Modal(document.getElementById('dataPreviewModal'));
        document.getElementById('dataPreviewContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载${dataType}数据预览...</p>
            </div>
        `;
        modal.show();
        
        // 模拟数据加载
        setTimeout(() => {
            document.getElementById('dataPreviewContent').innerHTML = `
                <p>这里将显示${dataType}的数据预览...</p>
            `;
        }, 1000);
    }
</script>
{% endblock %}
