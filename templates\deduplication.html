{% extends "base.html" %}

{% block title %}去重处理 - 实体匹配与去重系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    智能去重处理
                </h4>
            </div>
            <div class="card-body">
                {% if not completed %}
                <!-- 去重配置表单 -->
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-cogs me-2"></i>
                            去重参数配置
                        </h5>
                        
                        <form method="POST" id="deduplicationForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="similarity_threshold" class="form-label">
                                            <i class="fas fa-sliders-h me-2"></i>
                                            相似度阈值
                                        </label>
                                        <input type="range" class="form-range" id="similarity_threshold" 
                                               name="similarity_threshold" min="0.5" max="0.95" step="0.05" value="0.8">
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">0.5 (宽松)</small>
                                            <small class="text-muted" id="thresholdValue">0.8</small>
                                            <small class="text-muted">0.95 (严格)</small>
                                        </div>
                                        <small class="form-text text-muted">
                                            阈值越高，去重越严格；阈值越低，去重越宽松
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="method" class="form-label">
                                            <i class="fas fa-brain me-2"></i>
                                            相似度算法
                                        </label>
                                        <select class="form-select" id="method" name="method">
                                            <option value="combined" selected>组合算法（推荐）</option>
                                            <option value="levenshtein">Levenshtein距离</option>
                                            <option value="jaro_winkler">Jaro-Winkler相似度</option>
                                            <option value="jaccard">Jaccard系数</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            组合算法融合多种相似度计算方法，效果最佳
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>去重策略说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>Primary去重：</strong>识别primary.csv内部的重复实体</li>
                                    <li><strong>Alternate去重：</strong>移除与primary重复的alternate实体</li>
                                    <li><strong>保留策略：</strong>保留信息最完整的记录</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success btn-lg" id="deduplicationButton">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span class="button-text">
                                        <i class="fas fa-play me-2"></i>
                                        开始去重处理
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    参数建议
                                </h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-2"></i><strong>阈值 0.8：</strong>平衡精度和召回率</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>组合算法：</strong>综合多种相似度</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>处理时间：</strong>约1-3分钟</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card bg-light mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-pie text-info me-2"></i>
                                    预期效果
                                </h6>
                                <p class="small mb-0">
                                    去重处理将显著减少数据冗余，提高后续匹配的准确性和效率。
                                    系统会生成详细的去重报告。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <!-- 去重结果展示 -->
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>去重处理完成！</strong>已成功完成primary和alternate数据的去重处理。
                </div>
                
                <!-- 去重统计 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-database me-2"></i>
                                    Primary数据去重
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="metric-card">
                                            <div class="metric-value text-primary">{{ "{:,}".format(results.primary_original_count) }}</div>
                                            <div class="metric-label">原始记录数</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="metric-card">
                                            <div class="metric-value text-success">{{ "{:,}".format(results.primary_dedup_count) }}</div>
                                            <div class="metric-label">去重后记录数</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="metric-card">
                                            <div class="metric-value text-danger">{{ "{:,}".format(results.primary_original_count - results.primary_dedup_count) }}</div>
                                            <div class="metric-label">移除重复记录数</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="progress mt-3">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ (results.primary_dedup_count / results.primary_original_count * 100)|round(1) }}%">
                                        保留率: {{ (results.primary_dedup_count / results.primary_original_count * 100)|round(1) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-database me-2"></i>
                                    Alternate数据去重
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="metric-card">
                                            <div class="metric-value text-primary">{{ "{:,}".format(results.alternate_original_count) }}</div>
                                            <div class="metric-label">原始记录数</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="metric-card">
                                            <div class="metric-value text-success">{{ "{:,}".format(results.alternate_dedup_count) }}</div>
                                            <div class="metric-label">去重后记录数</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="metric-card">
                                            <div class="metric-value text-danger">{{ "{:,}".format(results.alternate_original_count - results.alternate_dedup_count) }}</div>
                                            <div class="metric-label">移除重复记录数</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="progress mt-3">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ (results.alternate_dedup_count / results.alternate_original_count * 100)|round(1) }}%">
                                        保留率: {{ (results.alternate_dedup_count / results.alternate_original_count * 100)|round(1) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 去重参数信息 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            去重参数配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>相似度阈值：</strong> {{ results.parameters.similarity_threshold }}</p>
                                <p><strong>算法方法：</strong> 
                                    {% if results.parameters.method == 'combined' %}
                                        组合算法
                                    {% elif results.parameters.method == 'levenshtein' %}
                                        Levenshtein距离
                                    {% elif results.parameters.method == 'jaro_winkler' %}
                                        Jaro-Winkler相似度
                                    {% elif results.parameters.method == 'jaccard' %}
                                        Jaccard系数
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>总去重率：</strong> 
                                    {{ ((results.primary_original_count + results.alternate_original_count - results.primary_dedup_count - results.alternate_dedup_count) / (results.primary_original_count + results.alternate_original_count) * 100)|round(2) }}%
                                </p>
                                <p><strong>处理状态：</strong> <span class="badge bg-success">完成</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 下一步操作 -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-search text-primary me-2"></i>
                                    实体匹配
                                </h5>
                                <p class="card-text">
                                    使用去重后的数据进行实体匹配
                                </p>
                                <a href="{{ url_for('matching') }}" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    开始匹配
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-download text-success me-2"></i>
                                    导出结果
                                </h5>
                                <p class="card-text">
                                    下载去重后的数据文件
                                </p>
                                <a href="{{ url_for('export_results') }}" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    导出文件
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <i class="fas fa-redo text-warning me-2"></i>
                                    重新去重
                                </h5>
                                <p class="card-text">
                                    使用不同参数重新执行去重
                                </p>
                                <button class="btn btn-warning" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>
                                    重新配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 阈值滑块实时更新
        const thresholdSlider = document.getElementById('similarity_threshold');
        const thresholdValue = document.getElementById('thresholdValue');
        
        if (thresholdSlider && thresholdValue) {
            thresholdSlider.addEventListener('input', function() {
                thresholdValue.textContent = this.value;
            });
        }
        
        // 表单提交处理
        const form = document.getElementById('deduplicationForm');
        const button = document.getElementById('deduplicationButton');
        
        if (form && button) {
            form.addEventListener('submit', function(e) {
                showLoading('deduplicationButton');
            });
        }
        
        {% if completed %}
        // 结果展示动画
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
        
        // 进度条动画
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'width 1s ease';
                bar.style.width = width;
            }, 500);
        });
        {% endif %}
    });
    
    // 算法说明
    function showAlgorithmInfo(method) {
        let description = '';
        switch(method) {
            case 'combined':
                description = '组合算法融合了Levenshtein距离、Jaro-Winkler相似度、Jaccard系数等多种算法，能够更准确地识别相似实体。';
                break;
            case 'levenshtein':
                description = 'Levenshtein距离计算两个字符串之间的编辑距离，适合处理拼写错误和字符变化。';
                break;
            case 'jaro_winkler':
                description = 'Jaro-Winkler相似度对前缀匹配给予更高权重，适合处理名称变体。';
                break;
            case 'jaccard':
                description = 'Jaccard系数基于集合相似度，适合处理词汇顺序变化的情况。';
                break;
        }
        
        alert(description);
    }
</script>
{% endblock %}
