#!/usr/bin/env python3
"""
测试首字母缩写匹配算法
"""

import pandas as pd
from modules.matcher import EntityMatcher

def test_initial_matching():
    print("=== 测试首字母缩写匹配算法 ===")
    
    # 创建测试数据
    test_cases = [
        ("SHELESTENKO, Hennadiy <PERSON>", "SHELESTENKO, Hennadiy O."),
        ("MALEKOUTI POUR, Hamidreza", "MALEKOUTI POUR, H."),
        ("TABATABAEI, Say<PERSON><PERSON>", "TABATABAEI, S A Akbar"),
        ("CIRE, Kursad Zafer", "CIRE, K. Z."),
        ("AGUILAR GARCIA, Marvin <PERSON>", "A GARCIA, Marvin R"),
    ]
    
    matcher = EntityMatcher()
    
    print("测试结果:")
    for original, abbreviated in test_cases:
        score = matcher._simple_similarity(original, abbreviated)
        print(f"原名: {original}")
        print(f"缩写: {abbreviated}")
        print(f"相似度: {score:.3f}")
        print(f"匹配: {'✓' if score >= 0.7 else '✗'}")
        print("-" * 50)

if __name__ == "__main__":
    test_initial_matching()
